import React, { useEffect, useRef } from 'react';
import QRCode from 'qrcode';
import { Loader2 } from 'lucide-react';

interface WhatsAppQRCodeProps {
  qrData: string | null;
  qrImageUrl: string | null;
  isLoading: boolean;
}

export function WhatsAppQRCode({ qrData, qrImageUrl, isLoading }: WhatsAppQRCodeProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (qrData && canvasRef.current) {
      QRCode.toCanvas(canvasRef.current, qrData, {
        width: 200,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
      });
    }
  }, [qrData]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200">
        <Loader2 className="h-10 w-10 text-indigo-600 animate-spin mb-4" />
        <p className="text-gray-600 text-center">Loading QR code...</p>
      </div>
    );
  }

  if (!qrData && !qrImageUrl) {
    return (
      <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200">
        <p className="text-gray-600 text-center">No QR code available</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200">
      {qrImageUrl ? (
        <img 
          src={qrImageUrl} 
          alt="WhatsApp QR Code" 
          className="w-48 h-48 mb-4"
        />
      ) : (
        <canvas ref={canvasRef} className="mb-4" />
      )}
      <p className="text-sm text-gray-600 text-center">
        Scan this QR code with WhatsApp on your phone to connect
      </p>
      <p className="text-xs text-gray-500 text-center mt-2">
        Open WhatsApp &gt; Menu &gt; Linked Devices &gt; Link a Device
      </p>
    </div>
  );
}
