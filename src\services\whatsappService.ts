import { supabase } from "../contexts/SimpleAuthContext";

// WhatsApp API base URL
export const API_BASE_URL = "https://wb-userbot-production.up.railway.app";

export interface WhatsAppSessionStatus {
  authenticated: boolean;
  qr_image_url?: string;
  raw_qr?: string;
  message?: string;
}

/**
 * Initialize a WhatsApp session for a salon
 * @param salonId The salon ID to initialize the session for
 * @returns The session status
 */
export const initializeWhatsAppSession = async (
  salonId: string,
): Promise<WhatsAppSessionStatus> => {
  try {
    console.log(`Initializing WhatsApp session for salon ${salonId}`);
    console.log(`API endpoint: ${API_BASE_URL}/session/${salonId}/init`);

    // First, make the initialization request
    const response = await fetch(`${API_BASE_URL}/session/${salonId}/init`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      // Adding mode: 'cors' to handle CORS issues
      mode: "cors",
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Server responded with ${response.status}: ${errorText}`);
      throw new Error(
        `Failed to initialize WhatsApp session: ${response.statusText}`,
      );
    }

    const result = await response.json();
    console.log("WhatsApp initialization successful:", result);

    return result;
  } catch (error) {
    console.error("Error initializing WhatsApp session:", error);
    // Provide more detailed error information
    if (
      error instanceof TypeError && error.message.includes("Failed to fetch")
    ) {
      console.error(
        "Network error: This could be due to CORS issues, network connectivity, or the API server being down",
      );
      console.error(
        "Please check that the WhatsApp API server is running at:",
        API_BASE_URL,
      );
    }
    throw error;
  }
};

/**
 * Get the QR code for a WhatsApp session
 * @param salonId The salon ID to get the QR code for
 * @returns The QR code data
 */
export const getWhatsAppQRCode = async (
  salonId: string,
): Promise<WhatsAppSessionStatus> => {
  try {
    console.log(`Getting WhatsApp QR code for salon ${salonId}`);
    console.log(`API endpoint: ${API_BASE_URL}/session/${salonId}/qr`);

    const response = await fetch(`${API_BASE_URL}/session/${salonId}/qr`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      mode: "cors",
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Server responded with ${response.status}: ${errorText}`);
      throw new Error(`Failed to get WhatsApp QR code: ${response.statusText}`);
    }

    const result = await response.json();
    console.log("WhatsApp QR code retrieved successfully");

    // Log if we got a QR code or not
    if (result.qr_image_url) {
      console.log("QR image URL received");
    } else if (result.raw_qr) {
      console.log("Raw QR data received");
    } else {
      console.warn("No QR code data in the response");
    }

    return result;
  } catch (error) {
    console.error("Error getting WhatsApp QR code:", error);
    if (
      error instanceof TypeError && error.message.includes("Failed to fetch")
    ) {
      console.error(
        "Network error: This could be due to CORS issues, network connectivity, or the API server being down",
      );
    }
    throw error;
  }
};

/**
 * Check the status of a WhatsApp session
 * @param salonId The salon ID to check the status for
 * @returns The session status
 */
export const checkWhatsAppSessionStatus = async (
  salonId: string,
): Promise<WhatsAppSessionStatus> => {
  try {
    console.log(`Checking WhatsApp session status for salon ${salonId}`);

    const response = await fetch(`${API_BASE_URL}/session/${salonId}/status`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      mode: "cors",
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Server responded with ${response.status}: ${errorText}`);
      throw new Error(
        `Failed to check WhatsApp session status: ${response.statusText}`,
      );
    }

    const result = await response.json();
    console.log("WhatsApp status check result:", result);

    return result;
  } catch (error) {
    console.error("Error checking WhatsApp session status:", error);
    if (
      error instanceof TypeError && error.message.includes("Failed to fetch")
    ) {
      console.error(
        "Network error: This could be due to CORS issues, network connectivity, or the API server being down",
      );
    }
    throw error;
  }
};

/**
 * Update the WhatsApp enabled status for a salon
 * @param salonId The salon ID to update
 * @param enabled Whether WhatsApp is enabled
 * @returns Success status
 */
export const updateWhatsAppStatus = async (
  salonId: string,
  enabled: boolean,
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("salons")
      .update({
        whatsapp_enabled: enabled,
        updated_at: new Date().toISOString(),
      })
      .eq("salon_id", salonId);

    if (error) {
      console.error("Error updating WhatsApp status:", error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error("Error in updateWhatsAppStatus:", error);
    return false;
  }
};

/**
 * Get the WhatsApp enabled status for a salon
 * @param salonId The salon ID to check
 * @returns Whether WhatsApp is enabled
 */
export const getWhatsAppStatus = async (salonId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from("salons")
      .select("whatsapp_enabled")
      .eq("salon_id", salonId)
      .single();

    if (error) {
      console.error("Error getting WhatsApp status:", error);
      throw error;
    }

    return data?.whatsapp_enabled || false;
  } catch (error) {
    console.error("Error in getWhatsAppStatus:", error);
    return false;
  }
};

/**
 * Send a WhatsApp message
 * @param salonId The salon ID to send the message from
 * @param phoneNumber The phone number to send the message to
 * @param message The message to send
 * @returns Success status
 */
export const sendWhatsAppMessage = async (
  salonId: string,
  phoneNumber: string,
  message: string,
): Promise<{ success: boolean; message: string; qr_endpoint?: string }> => {
  try {
    console.log(
      `Sending WhatsApp message for salon ${salonId} to ${phoneNumber}`,
    );

    // Format phone number (remove any non-digit characters and ensure it starts with country code)
    let formattedPhone = phoneNumber.replace(/\D/g, "");
    if (!formattedPhone.startsWith("91") && formattedPhone.length === 10) {
      formattedPhone = "91" + formattedPhone;
    }

    console.log(`Formatted phone number: ${formattedPhone}`);
    console.log(`API endpoint: ${API_BASE_URL}/session/${salonId}/send`);

    const response = await fetch(`${API_BASE_URL}/session/${salonId}/send`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      mode: "cors",
      body: JSON.stringify({
        phoneNumber: formattedPhone,
        message: message,
      }),
    });

    const result = await response.json();
    console.log("WhatsApp send message response:", result);

    if (!response.ok) {
      // Handle WhatsApp authentication error
      if (response.status === 403 && result.qr_endpoint) {
        console.error("WhatsApp authentication error:", result.message);
        return {
          success: false,
          message: result.message ||
            "WhatsApp connection not authenticated. Please scan the QR code first.",
          qr_endpoint: result.qr_endpoint,
        };
      }

      throw new Error(result.message || "Failed to send WhatsApp message");
    }

    console.log("WhatsApp message sent successfully");
    return { success: true, message: "Message sent successfully" };
  } catch (error) {
    console.error("Error sending WhatsApp message:", error);
    if (
      error instanceof TypeError && error.message.includes("Failed to fetch")
    ) {
      console.error(
        "Network error: This could be due to CORS issues, network connectivity, or the API server being down",
      );
      return {
        success: false,
        message:
          "Network error: Unable to connect to the WhatsApp API server. Please check your internet connection and try again.",
      };
    }
    return {
      success: false,
      message: error.message || "Failed to send WhatsApp message",
    };
  }
};

/**
 * Reset WhatsApp session (clear conflicts)
 * @param salonId The salon ID to reset the session for
 * @returns Success status and message
 */
export const resetWhatsAppSession = async (
  salonId: string,
): Promise<{ success: boolean; message: string; was_conflicted?: boolean }> => {
  try {
    console.log(`Resetting WhatsApp session for salon ${salonId}`);

    const response = await fetch(`${API_BASE_URL}/session/${salonId}/reset`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      mode: "cors",
    });

    const result = await response.json();
    console.log("WhatsApp session reset response:", result);

    if (!response.ok) {
      throw new Error(result.message || "Failed to reset WhatsApp session");
    }

    return {
      success: result.success || false,
      message: result.message || "Session reset successfully",
    };
  } catch (error) {
    console.error("Error resetting WhatsApp session:", error);
    return {
      success: false,
      message: (error as Error).message || "Failed to reset WhatsApp session",
    };
  }
};

/**
 * Disconnect WhatsApp session completely (clear everything)
 * @param salonId The salon ID to disconnect
 * @returns Success status and message
 */
export const disconnectWhatsAppSession = async (
  salonId: string,
): Promise<{ success: boolean; message: string }> => {
  try {
    console.log(`Disconnecting WhatsApp session for salon ${salonId}`);

    const response = await fetch(`${API_BASE_URL}/session/${salonId}/reset`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      mode: "cors",
    });

    const result = await response.json();
    console.log("WhatsApp disconnect response:", result);

    if (!response.ok) {
      throw new Error(
        result.message || "Failed to disconnect WhatsApp session",
      );
    }

    return {
      success: result.success || false,
      message: "WhatsApp disconnected successfully. You can now reconnect.",
    };
  } catch (error) {
    console.error("Error disconnecting WhatsApp session:", error);
    return {
      success: false,
      message: (error as Error).message ||
        "Failed to disconnect WhatsApp session",
    };
  }
};
