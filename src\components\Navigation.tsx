import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Menu, X, Home, Users, Calendar, Scissors, UserCircle, LogOut, ChevronDown, Settings, Store, User } from 'lucide-react';
import { useAuth } from '../contexts/SimpleAuthContext';
import { supabase } from '../contexts/SimpleAuthContext';

interface NavigationProps {
  onLogout: () => void;
}

export function Navigation({ onLogout }: NavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [userName, setUserName] = useState('');
  const [userInitials, setUserInitials] = useState('');
  const [salonName, setSalonName] = useState('SalonFlow');
  const [appVersion, setAppVersion] = useState('1.0.0');
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Fetch user and salon data
  useEffect(() => {
    const fetchUserData = async () => {
      if (user?.id) {
        try {
          // Get user profile data
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('first_name, last_name')
            .eq('user_id', user.id)
            .single();

          if (userError) throw userError;

          if (userData) {
            const fullName = `${userData.first_name || ''} ${userData.last_name || ''}`.trim();
            setUserName(fullName || user.email);

            // Create initials from name
            const initials = userData.first_name && userData.last_name
              ? `${userData.first_name[0]}${userData.last_name[0]}`.toUpperCase()
              : userData.first_name
                ? userData.first_name[0].toUpperCase()
                : user.email ? user.email[0].toUpperCase() : 'U';

            setUserInitials(initials);
          }

          // Get salon data
          const { data: salonData, error: salonError } = await supabase
            .from('salons')
            .select('name')
            .eq('owner_user_id', user.id)
            .single();

          if (!salonError && salonData?.name) {
            setSalonName(salonData.name);
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      }
    };

    fetchUserData();
  }, [user]);

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Appointments', href: '/appointments', icon: Calendar },
    { name: 'Clients', href: '/clients', icon: Users },
    { name: 'Services', href: '/services', icon: Scissors },
    { name: 'Staff', href: '/staff', icon: UserCircle },
  ];

  return (
    <nav className="bg-white shadow-md border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div
              className="flex-shrink-0 flex items-center cursor-pointer"
              onClick={() => navigate('/dashboard')}
            >
              <div className="flex items-center">
                <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 transition-all">
                  {salonName || "Salon Orbit"}
                </span>
                <span className="ml-1.5 text-xs font-medium text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">Lite</span>
              </div>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="inline-flex items-center justify-center p-2 rounded-full text-indigo-600 hover:text-indigo-700 hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all"
              aria-label="Menu"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>

          {/* Desktop menu */}
          <div className="hidden md:flex md:items-center md:space-x-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`
                    inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200
                    ${isActive
                      ? 'text-indigo-600 bg-indigo-50'
                      : 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50/50'
                    }
                  `}
                >
                  <Icon className={`h-4 w-4 mr-2 ${isActive ? 'text-indigo-600' : 'text-gray-500'}`} />
                  {item.name}
                </Link>
              );
            })}
          </div>

          {/* User profile dropdown */}
          <div className="hidden md:flex md:items-center">
            <div className="relative">
              <button
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600 focus:outline-none transition-all duration-200 ml-4 rounded-full hover:bg-indigo-50 px-2 py-1"
                aria-label="User menu"
              >
                <div className="h-8 w-8 rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 flex items-center justify-center text-white font-medium mr-2 shadow-sm">
                  {userInitials || 'U'}
                </div>
                <span className="mr-1">{userName || 'User'}</span>
                <ChevronDown className="h-4 w-4" />
              </button>

              {isProfileOpen && (
                <>
                  {/* Backdrop for desktop dropdown */}
                  <div
                    className="fixed inset-0 z-10"
                    onClick={() => setIsProfileOpen(false)}
                  ></div>

                  <div className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-xl py-1 z-20 border border-gray-100 overflow-hidden">
                    <div className="px-4 py-3 bg-gradient-to-r from-indigo-600/10 to-purple-600/10 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">{userName || 'User'}</p>
                      <p className="text-xs text-gray-500 mt-1 truncate">{user?.email}</p>
                    </div>

                    <div className="py-1">
                      <Link
                        to="/settings"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center transition-colors duration-150"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <Settings className="h-4 w-4 mr-2 text-gray-500" />
                        Settings
                      </Link>
                    </div>

                    <div className="border-t border-gray-100 pt-1">
                      <button
                        onClick={() => {
                          setIsProfileOpen(false);
                          onLogout();
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors duration-150"
                      >
                        <LogOut className="h-4 w-4 mr-2 text-red-500" />
                        Logout
                      </button>
                    </div>

                    <div className="border-t border-gray-100 px-4 py-2 bg-gray-50">
                      <p className="text-xs text-gray-400 text-center">
                        <span className="block font-medium text-indigo-600 mb-1">Salon Orbit Lite</span>
                        Version {appVersion}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu - Slide in from the right */}
      {isOpen && (
        <>
          {/* Backdrop overlay */}
          <div
            className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40 md:hidden"
            onClick={() => setIsOpen(false)}
          ></div>

          {/* Menu panel */}
          <div className="fixed inset-y-0 right-0 w-[80%] max-w-sm bg-white shadow-xl z-50 md:hidden transform transition-transform duration-300 ease-in-out">
            <div className="h-full flex flex-col">
              {/* User profile at the top */}
              <div className="p-6 bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white font-medium text-lg mr-3 shadow-lg">
                    {userInitials || 'U'}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{userName || 'User'}</h3>
                    <p className="text-xs text-white/80 truncate">{user?.email}</p>
                  </div>
                </div>
              </div>

              {/* Navigation links */}
              <div className="flex-1 overflow-y-auto py-4 px-2">
                <div className="space-y-1 px-2">
                  {navigation.map((item) => {
                    const Icon = item.icon;
                    const isActive = location.pathname === item.href;
                    return (
                      <Link
                        key={item.name}
                        to={item.href}
                        className={`
                          flex items-center px-4 py-3 rounded-xl text-base font-medium transition-all duration-200
                          ${isActive
                            ? 'bg-indigo-50 text-indigo-600 shadow-sm'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600'
                          }
                        `}
                        onClick={() => setIsOpen(false)}
                      >
                        <div className={`
                          h-9 w-9 rounded-full flex items-center justify-center mr-3
                          ${isActive
                            ? 'bg-indigo-100 text-indigo-600'
                            : 'bg-gray-100 text-gray-500'
                          }
                        `}>
                          <Icon className="h-5 w-5" />
                        </div>
                        {item.name}
                      </Link>
                    );
                  })}
                </div>

                <div className="mt-6 px-2">
                  <div className="h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent my-4"></div>

                  <Link
                    to="/settings"
                    className="flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-indigo-600 transition-all duration-200"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="h-9 w-9 rounded-full bg-gray-100 flex items-center justify-center text-gray-500 mr-3">
                      <Settings className="h-5 w-5" />
                    </div>
                    Settings
                  </Link>

                  <button
                    onClick={() => {
                      setIsOpen(false);
                      onLogout();
                    }}
                    className="w-full flex items-center px-4 py-3 rounded-xl text-base font-medium text-red-600 hover:bg-red-50 transition-all duration-200 mt-2"
                  >
                    <div className="h-9 w-9 rounded-full bg-red-50 flex items-center justify-center text-red-500 mr-3">
                      <LogOut className="h-5 w-5" />
                    </div>
                    Logout
                  </button>
                </div>
              </div>

              {/* App version at the bottom */}
              <div className="p-4 border-t border-gray-100">
                <p className="text-xs text-gray-400 text-center">
                  <span className="block font-medium text-indigo-600 mb-1">Salon Orbit Lite</span>
                  Version {appVersion}
                </p>
              </div>
            </div>
          </div>
        </>
      )}
    </nav>
  );
}