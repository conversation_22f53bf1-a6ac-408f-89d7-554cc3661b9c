import React, { useState, useEffect, useCallback, useRef } from 'react';
import { User, Store, Save, Loader2, MessageSquare, CheckCircle, Link as LinkIcon, Copy, ExternalLink } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Switch } from '../components/ui/switch';
import { useToast } from '../components/ui/use-toast';
import { useAuth } from '../contexts/SimpleAuthContext';
import { useSalon } from '../contexts/SalonContext';
import { supabase } from '../contexts/SimpleAuthContext';
import { WhatsAppQRCode } from '../components/WhatsAppQRCode';
import { WhatsAppTroubleshootingGuide } from '../components/WhatsAppTroubleshootingGuide';
import {
  initializeWhatsAppSession,
  getWhatsAppQRCode,
  checkWhatsAppSessionStatus,
  updateWhatsAppStatus,
  getWhatsAppStatus,
  disconnectWhatsAppSession,
  API_BASE_URL
} from '../services/whatsappService';

export function Settings() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [salonName, setSalonName] = useState('');
  const [salonAddress, setSalonAddress] = useState('');
  const [salonId, setSalonId] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [whatsappEnabled, setWhatsappEnabled] = useState(false);
  const [whatsappConnecting, setWhatsappConnecting] = useState(false);
  const [whatsappQrData, setWhatsappQrData] = useState<string | null>(null);
  const [whatsappQrImageUrl, setWhatsappQrImageUrl] = useState<string | null>(null);
  const [whatsappAuthenticated, setWhatsappAuthenticated] = useState(false);
  const [pollingInterval, setPollingInterval] = useState<number | null>(null);
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const { salonData, updateSalonData } = useSalon();

  // Fetch user and salon data
  useEffect(() => {
    const fetchData = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Get user profile data
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('first_name, last_name')
          .eq('user_id', user.id)
          .single();

        if (userError) throw userError;

        if (userData) {
          setFirstName(userData.first_name || '');
          setLastName(userData.last_name || '');
        }

        // Use salon data from context
        if (salonData) {
          setSalonName(salonData.name || '');
          setSalonAddress(salonData.address || '');
          setSalonId(salonData.salon_id);

          // Set WhatsApp status
          const isWhatsappEnabled = salonData.whatsapp_enabled || false;
          setWhatsappEnabled(isWhatsappEnabled);

          // If WhatsApp is enabled, check if it's authenticated
          if (isWhatsappEnabled && salonData.salon_id) {
            try {
              const status = await checkWhatsAppSessionStatus(salonData.salon_id);
              console.log('WhatsApp status on load:', status);

              // Simple status check - if authenticated and healthy, show as connected
              if (status.authenticated && status.status === 'healthy') {
                setWhatsappAuthenticated(true);
                setWhatsappConnecting(false);
              } else if (status.status === 'conflict') {
                // If conflicted, show as disconnected and suggest reconnection
                setWhatsappAuthenticated(false);
                setWhatsappConnecting(false);
                setShowTroubleshooting(true);
              } else {
                // If not authenticated or any other issues, show as disconnected
                setWhatsappAuthenticated(false);
                setWhatsappConnecting(false);
              }
            } catch (error) {
              console.error('Error checking WhatsApp status:', error);
              // On error, assume disconnected
              setWhatsappAuthenticated(false);
              setWhatsappConnecting(false);
            }
          } else {
            // If WhatsApp is disabled, reset authentication state
            setWhatsappAuthenticated(false);
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load settings. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, salonData]);

  const validateForm = () => {
    const errors: {[key: string]: string} = {};

    // Validate personal information
    if (!firstName || !firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!lastName || !lastName.trim()) {
      errors.lastName = 'Last name is required';
    }

    // Validate salon information
    if (!salonName || !salonName.trim()) {
      errors.salonName = 'Salon name is required';
    }


    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = async () => {
    if (!user?.id) return;

    // Additional safety check for all required fields
    if (!firstName?.trim() || !lastName?.trim() || !salonName?.trim()) {
      toast({
        title: 'Validation Error',
        description: 'First name, last name, and salon name are all required.',
        variant: 'destructive',
      });

      // Set validation errors for empty fields
      const errors: {[key: string]: string} = {};
      if (!firstName?.trim()) errors.firstName = 'First name is required';
      if (!lastName?.trim()) errors.lastName = 'Last name is required';
      if (!salonName?.trim()) errors.salonName = 'Salon name is required';
      setValidationErrors(errors);
      return;
    }

    // Validate form
    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors below and try again.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);
      setValidationErrors({});

      // Update user profile
      const { error: userError } = await supabase
        .from('users')
        .update({
          first_name: firstName,
          last_name: lastName,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id);

      if (userError) throw userError;

      // Update salon name and address if we have a salon ID
      if (salonId) {
        const { error: salonError } = await supabase
          .from('salons')
          .update({
            name: salonName,
            address: salonAddress,
            whatsapp_enabled: whatsappEnabled,
            updated_at: new Date().toISOString(),
          })
          .eq('salon_id', salonId);

        if (salonError) throw salonError;

        // Update salon context with new data
        updateSalonData({
          name: salonName,
          address: salonAddress,
          whatsapp_enabled: whatsappEnabled,
        });
      }

      toast({
        title: 'Success',
        description: 'Settings saved successfully',
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle WhatsApp toggle
  const handleWhatsAppToggle = async (enabled: boolean) => {
    setWhatsappEnabled(enabled);

    // Save the WhatsApp enabled status to database immediately
    if (salonId) {
      try {
        const { error: salonError } = await supabase
          .from('salons')
          .update({
            whatsapp_enabled: enabled,
            updated_at: new Date().toISOString(),
          })
          .eq('salon_id', salonId);

        if (salonError) {
          console.error('Error updating WhatsApp status:', salonError);
          toast({
            title: 'Error',
            description: 'Failed to save WhatsApp setting. Please try again.',
            variant: 'destructive',
          });
          // Revert the toggle if save failed
          setWhatsappEnabled(!enabled);
          return;
        }

        // Update salon context
        updateSalonData({ whatsapp_enabled: enabled });
      } catch (error) {
        console.error('Error saving WhatsApp status:', error);
        toast({
          title: 'Error',
          description: 'Failed to save WhatsApp setting. Please try again.',
          variant: 'destructive',
        });
        // Revert the toggle if save failed
        setWhatsappEnabled(!enabled);
        return;
      }
    }

    if (enabled && salonId) {
      await initializeWhatsAppConnection();
    } else {
      // If disabling, clear any polling intervals
      if (pollingInterval) {
        clearInterval(pollingInterval);
        setPollingInterval(null);
      }

      // Reset WhatsApp states
      setWhatsappQrData(null);
      setWhatsappQrImageUrl(null);
      setWhatsappConnecting(false);
      setWhatsappAuthenticated(false);
    }
  };

  // Initialize WhatsApp connection
  const initializeWhatsAppConnection = async () => {
    if (!salonId) return;

    try {
      setWhatsappConnecting(true);
      setShowTroubleshooting(false);

      // Initialize WhatsApp session
      await initializeWhatsAppSession(salonId);

      // Check if already connected first
      try {
        const status = await checkWhatsAppSessionStatus(salonId);
        console.log('Status check before QR generation:', status);

        if (status.authenticated) {
          // Already connected!
          setWhatsappAuthenticated(true);
          setWhatsappConnecting(false);
          toast({
            title: 'WhatsApp Already Connected',
            description: 'Your WhatsApp account is already connected and ready!',
          });
          return;
        }

        // If status is 'healthy' or 'awaiting_qr', the session is active
        if (status.status === 'healthy') {
          setWhatsappAuthenticated(true);
          setWhatsappConnecting(false);
          toast({
            title: 'WhatsApp Connected',
            description: 'Your WhatsApp account is connected and ready!',
          });
          return;
        }
      } catch (statusError) {
        console.log('Status check failed, proceeding with QR code generation:', statusError);
      }

      // Get QR code
      const qrResponse = await getWhatsAppQRCode(salonId);

      if (!qrResponse.raw_qr && !qrResponse.qr_image_url) {
        throw new Error('No QR code received from the server. The WhatsApp API may be unavailable.');
      }

      setWhatsappQrData(qrResponse.raw_qr || null);
      setWhatsappQrImageUrl(qrResponse.qr_image_url || null);

      // Start polling for status
      startStatusPolling();

    } catch (error) {
      console.error('Error initializing WhatsApp:', error);

      let errorMessage = 'Failed to initialize WhatsApp connection. Please try again.';

      // Handle "already connected" case
      if (error.message.includes('already connected') || error.message.includes('No QR code available')) {
        // Check status to confirm connection or detect conflicts
        try {
          const status = await checkWhatsAppSessionStatus(salonId);
          console.log('Status after QR error:', status);

          if (status.authenticated) {
            setWhatsappAuthenticated(true);
            setWhatsappConnecting(false);
            toast({
              title: 'WhatsApp Connected',
              description: 'Your WhatsApp account is already connected!',
            });
            return;
          } else if (status.status === 'conflict') {
            // Handle conflict case
            errorMessage = 'WhatsApp session conflict detected. Please clear other WhatsApp Web sessions and try again.';
            setShowTroubleshooting(true);
          } else {
            errorMessage = 'WhatsApp session is already active but not authenticated. Please try resetting the connection.';
            setShowTroubleshooting(true);
          }
        } catch (statusError) {
          console.error('Failed to check status after QR error:', statusError);
          errorMessage = 'WhatsApp session is already active. If you need to reconnect, please contact support.';
        }
      } else if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        errorMessage = 'Unable to connect to the WhatsApp API server. Please check your internet connection and try again later.';
        setShowTroubleshooting(true);
      } else if (error.message.includes('No QR code received')) {
        errorMessage = error.message;
      }

      toast({
        title: 'WhatsApp Connection Error',
        description: errorMessage,
        variant: 'destructive',
      });

      setWhatsappConnecting(false);
      // Don't disable WhatsApp if it's already connected
      if (!error.message.includes('already connected') && !error.message.includes('No QR code available')) {
        setWhatsappEnabled(false);
      }
    }
  };

  // Start polling for WhatsApp connection status
  const startStatusPolling = useCallback(() => {
    if (!salonId) return;

    // Clear any existing interval
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    // Set up new polling interval
    const intervalId = window.setInterval(async () => {
      try {
        const status = await checkWhatsAppSessionStatus(salonId);

        if (status.authenticated) {
          // WhatsApp is connected
          setWhatsappAuthenticated(true);
          setWhatsappConnecting(false);

          // Stop polling
          clearInterval(intervalId);
          setPollingInterval(null);

          toast({
            title: 'WhatsApp Connected',
            description: 'Your WhatsApp account is now connected successfully!',
          });
        }
      } catch (error) {
        console.error('Error checking WhatsApp status:', error);

        // If we get a network error, stop polling after a few retries
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
          // Count consecutive failures
          const failCount = (window as any).whatsappStatusFailCount || 0;
          (window as any).whatsappStatusFailCount = failCount + 1;

          // After 3 consecutive failures, stop polling and show an error
          if (failCount >= 2) {
            clearInterval(intervalId);
            setPollingInterval(null);
            setWhatsappConnecting(false);

            toast({
              title: 'WhatsApp Connection Error',
              description: 'Unable to check WhatsApp connection status. The server may be unavailable.',
              variant: 'destructive',
            });

            // Show troubleshooting guide
            setShowTroubleshooting(true);

            // Reset fail count
            (window as any).whatsappStatusFailCount = 0;
          }
        }
      }
    }, 5000); // Check every 5 seconds

    setPollingInterval(intervalId);

    // Clean up interval on component unmount
    return () => {
      clearInterval(intervalId);
      // Reset fail count
      (window as any).whatsappStatusFailCount = 0;
    };
  }, [salonId, toast]);

  // Disconnect WhatsApp completely
  const disconnectWhatsApp = async () => {
    if (!salonId) return;

    try {
      setWhatsappConnecting(true);

      const result = await disconnectWhatsAppSession(salonId);

      if (result.success) {
        // Reset all states
        setWhatsappAuthenticated(false);
        setWhatsappQrData(null);
        setWhatsappQrImageUrl(null);
        setShowTroubleshooting(false);

        toast({
          title: 'WhatsApp Disconnected',
          description: 'WhatsApp has been disconnected. You can now reconnect with a fresh QR code.',
        });
      } else {
        toast({
          title: 'Disconnect Failed',
          description: result.message || 'Failed to disconnect WhatsApp. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error disconnecting WhatsApp:', error);
      toast({
        title: 'Disconnect Failed',
        description: 'Failed to disconnect WhatsApp session. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setWhatsappConnecting(false);
    }
  };

  // Clean up polling interval on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>

      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center mb-4">
            <User className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">Personal Information</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={firstName}
                onChange={(e) => {
                  const value = e.target.value;
                  setFirstName(value);
                  // Clear validation error when user starts typing
                  if (validationErrors.firstName && value.trim()) {
                    setValidationErrors(prev => ({ ...prev, firstName: '' }));
                  }
                }}
                placeholder="Enter your first name"
                required
                className={validationErrors.firstName ? 'border-red-500' : ''}
              />
              {validationErrors.firstName && (
                <p className="text-sm text-red-600">{validationErrors.firstName}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={lastName}
                onChange={(e) => {
                  const value = e.target.value;
                  setLastName(value);
                  // Clear validation error when user starts typing
                  if (validationErrors.lastName && value.trim()) {
                    setValidationErrors(prev => ({ ...prev, lastName: '' }));
                  }
                }}
                placeholder="Enter your last name"
                required
                className={validationErrors.lastName ? 'border-red-500' : ''}
              />
              {validationErrors.lastName && (
                <p className="text-sm text-red-600">{validationErrors.lastName}</p>
              )}
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                value={user?.email || ''}
                disabled
                className="bg-gray-50 text-gray-500"
              />
              <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center mb-4">
            <Store className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">Salon Information</h2>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="salonName">Salon Name</Label>
              <Input
                id="salonName"
                value={salonName}
                onChange={(e) => {
                  const value = e.target.value;
                  setSalonName(value);
                  // Clear validation error when user starts typing
                  if (validationErrors.salonName && value.trim()) {
                    setValidationErrors(prev => ({ ...prev, salonName: '' }));
                  }
                }}
                placeholder="Enter your salon name"
                required
                className={validationErrors.salonName ? 'border-red-500' : ''}
              />
              {validationErrors.salonName && (
                <p className="text-sm text-red-600">{validationErrors.salonName}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="salonAddress">Salon Address</Label>
              <Input
                id="salonAddress"
                value={salonAddress}
                onChange={(e) => setSalonAddress(e.target.value)}
                placeholder="Enter your salon address (optional)"
              />
            </div>
          </div>
        </div>

        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center mb-4">
            <MessageSquare className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">WhatsApp Integration</h2>
          </div>

          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-base font-medium text-gray-900">Enable WhatsApp Notifications</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Connect your WhatsApp account to send appointment and billing notifications to clients
                </p>
              </div>
              <Switch
                checked={whatsappEnabled}
                onChange={(e) => handleWhatsAppToggle(e.target.checked)}
              />
            </div>

            {whatsappEnabled && (
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  {whatsappAuthenticated ? (
                    <div className="flex flex-col items-center text-center p-4">
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-1">WhatsApp Connected</h3>
                      <p className="text-sm text-gray-500">
                        Your WhatsApp account is successfully connected and ready to send notifications
                      </p>
                      <div className="flex flex-col sm:flex-row justify-center gap-3 mt-4">
                        <Button
                          variant="outline"
                          onClick={disconnectWhatsApp}
                          disabled={whatsappConnecting}
                        >
                          {whatsappConnecting ? 'Disconnecting...' : 'Disconnect WhatsApp'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowTroubleshooting(!showTroubleshooting)}
                        >
                          {showTroubleshooting ? 'Hide' : 'Show'} Troubleshooting Guide
                        </Button>
                      </div>
                    </div>
                  ) : whatsappConnecting ? (
                    <div className="space-y-4">
                      <div className="text-center mb-4">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Connect WhatsApp</h3>
                        <p className="text-sm text-gray-500">
                          Scan this QR code with your WhatsApp to connect your account
                        </p>
                      </div>

                      <WhatsAppQRCode
                        qrData={whatsappQrData}
                        qrImageUrl={whatsappQrImageUrl}
                        isLoading={whatsappConnecting && !whatsappQrData && !whatsappQrImageUrl}
                      />

                      <div className="text-center mt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowTroubleshooting(!showTroubleshooting)}
                        >
                          {showTroubleshooting ? 'Hide' : 'Show'} Troubleshooting Guide
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center p-4">
                      <p className="text-sm text-gray-500">
                        Click the button below to connect your WhatsApp account
                      </p>
                      <div className="flex flex-col sm:flex-row justify-center gap-3 mt-3">
                        <Button
                          onClick={initializeWhatsAppConnection}
                          disabled={whatsappConnecting}
                        >
                          {whatsappConnecting ? 'Connecting...' : 'Connect WhatsApp'}
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => setShowTroubleshooting(!showTroubleshooting)}
                        >
                          {showTroubleshooting ? 'Hide' : 'Show'} Troubleshooting Guide
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {showTroubleshooting && (
                  <WhatsAppTroubleshootingGuide
                    onReconnect={initializeWhatsAppConnection}
                    apiBaseUrl={API_BASE_URL}
                  />
                )}
              </div>
            )}
          </div>
        </div>

        <div className="p-6 flex justify-end">
          <div className="flex flex-col items-end">
            {(!firstName?.trim() || !lastName?.trim() || !salonName?.trim()) && (
              <p className="text-sm text-red-600 mb-2">
                {!firstName?.trim() && !lastName?.trim() && !salonName?.trim()
                  ? 'First name, last name, and salon name are required'
                  : !firstName?.trim() && !lastName?.trim()
                  ? 'First name and last name are required'
                  : !firstName?.trim() && !salonName?.trim()
                  ? 'First name and salon name are required'
                  : !lastName?.trim() && !salonName?.trim()
                  ? 'Last name and salon name are required'
                  : !firstName?.trim()
                  ? 'First name is required'
                  : !lastName?.trim()
                  ? 'Last name is required'
                  : 'Salon name is required'
                }
              </p>
            )}
            <Button
              onClick={handleSave}
              disabled={saving || !firstName?.trim() || !lastName?.trim() || !salonName?.trim()}
              className="flex items-center"
            >
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </Button>
          </div>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg overflow-hidden mb-8">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center mb-4">
            <LinkIcon className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">Client Booking App</h2>
          </div>

          <div className="space-y-4">
            <p className="text-sm text-gray-500">
              Share this link with your clients to allow them to book appointments directly.
            </p>

            {salonId && (
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="flex-1 relative">
                  <Input
                    value={`https://salonorbitbooking.netlify.app/salon/${salonId}`}
                    readOnly
                    className="pr-10 bg-gray-50"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full"
                    onClick={() => {
                      navigator.clipboard.writeText(`https://salonorbitbooking.netlify.app/salon/${salonId}`);
                      toast({
                        title: "Copied!",
                        description: "Booking URL copied to clipboard",
                      });
                    }}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <Button
                  onClick={() => window.open(`https://salonorbitbooking.netlify.app/salon/${salonId}`, '_blank')}
                  className="flex items-center"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open Booking App
                </Button>
              </div>
            )}

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <p className="text-sm text-blue-800">
                <strong>Note:</strong> The booking app is hosted at salonorbitbooking.netlify.app.
                Share this link with your clients to allow them to book appointments directly.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="text-center text-xs text-gray-400">
        <div className="font-medium text-indigo-600 mb-1">Salon Orbit Lite</div>
        Version 1.0.0
      </div>
    </div>
  );
}
