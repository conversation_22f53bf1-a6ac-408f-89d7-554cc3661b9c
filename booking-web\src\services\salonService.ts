import { supabase } from '../lib/supabase';
import { Salon } from '../types/database';

/**
 * Get all salons
 * @returns Array of salons
 */
export const getSalons = async (): Promise<Salon[]> => {
  try {
    const { data, error } = await supabase
      .from('salons')
      .select('*');

    if (error) {
      console.error('Error fetching salons:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getSalons:', error);
    return [];
  }
};

/**
 * Get a salon by ID
 * @param salonId The salon ID
 * @returns The salon if found, null otherwise
 */
export const getSalonById = async (salonId: string): Promise<Salon | null> => {
  try {
    const { data, error } = await supabase
      .from('salons')
      .select('*')
      .eq('salon_id', salonId)
      .single();

    if (error) {
      console.error('Error fetching salon:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getSalonById:', error);
    return null;
  }
};
