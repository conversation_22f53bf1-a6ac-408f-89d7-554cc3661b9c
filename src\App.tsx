import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { Auth } from './pages/Auth';
import { ResetPassword } from './pages/ResetPassword';
import { Dashboard } from './pages/Dashboard';
import { Clients } from './pages/Clients';
import { Appointments } from './pages/Appointments';
import { Services } from './pages/Services';
import { Staff } from './pages/Staff';
import { Settings } from './pages/Settings';
import { Layout } from './components/Layout';
import { MobileLayout } from './components/MobileLayout';
import { AuthProvider, useAuth } from './contexts/SimpleAuthContext';
import { Toaster } from './components/ui/toast';

// Mobile detection hook
function useMobileDetection() {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return isMobile;
}

// Responsive layout selector
function getResponsiveLayout() {
  const isMobile = useMobileDetection();
  return isMobile ? <MobileLayout /> : <Layout />;
}

// AppRoutes component to use the auth context
function AppRoutes() {
  const { isAuthenticated, loading } = useAuth();

  // Show loading indicator while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600 mb-4"></div>
        <p className="text-gray-500">Loading your account...</p>
      </div>
    );
  }

  return (
    <Routes>
      <Route
        path="/auth"
        element={
          !isAuthenticated ? (
            <Auth />
          ) : (
            <Navigate to="/dashboard" replace />
          )
        }
      />
      <Route
        path="/reset-password"
        element={<ResetPassword />}
      />
      <Route
        path="/"
        element={isAuthenticated ? getResponsiveLayout() : <Navigate to="/auth" replace />}
      >
        <Route path="dashboard" element={<Dashboard />} />
        <Route path="clients" element={<Clients />} />
        <Route path="appointments" element={<Appointments />} />
        <Route path="services" element={<Services />} />
        <Route path="staff" element={<Staff />} />
        <Route path="settings" element={<Settings />} />
        <Route index element={<Navigate to="/dashboard" replace />} />
      </Route>
    </Routes>
  );
}

// Wrapper component that uses the Router context
function AppWithRouter() {
  return <AppRoutes />;
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppWithRouter />
        <Toaster />
      </Router>
    </AuthProvider>
  );
}

export default App;