// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/supabase

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface AppointmentPayload {
  type: "INSERT" | "UPDATE" | "DELETE";
  table: string;
  record: {
    appointment_id: string;
    salon_id: string;
    client_id: string;
    appointment_date: string;
    duration?: number;
    status: "scheduled" | "checked-in" | "completed" | "cancelled" | "no_show";
    notes?: string;
    staff_id?: string;
    created_at: string;
    updated_at: string;
    old_status?: string;
    old_appointment_date?: string;
  };
  old_record?: {
    appointment_id: string;
    salon_id: string;
    client_id: string;
    appointment_date: string;
    duration?: number;
    status: "scheduled" | "checked-in" | "completed" | "cancelled" | "no_show";
    notes?: string;
    staff_id?: string;
    created_at: string;
    updated_at: string;
  };
}

interface Client {
  client_id: string;
  salon_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  address?: string;
  created_at: string;
  updated_at: string;
  is_active?: boolean;
}

interface Salon {
  salon_id: string;
  owner_user_id: string;
  name: string;
  address?: string;
  whatsapp_enabled: boolean;
  created_at: string;
  updated_at: string;
}

interface Service {
  service_id: string;
  name: string;
  price: number;
  duration?: number;
}

interface Staff {
  user_id: string;
  first_name: string;
  last_name: string;
}

interface WhatsAppSessionStatus {
  authenticated: boolean;
  qr_image_url?: string;
  raw_qr?: string;
  message?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("🔔 Appointment notification function called");

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    );

    // Get the request payload
    const payload: AppointmentPayload = await req.json();
    console.log("📦 Received payload:", JSON.stringify(payload, null, 2));

    // Only process appointments table events
    if (payload.table !== "appointments") {
      return new Response(
        JSON.stringify({ message: "Not an appointment event" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        },
      );
    }

    // Get the salon information to check if WhatsApp is enabled
    const { data: salonData, error: salonError } = await supabaseClient
      .from("salons")
      .select("*")
      .eq("salon_id", payload.record.salon_id)
      .single();

    if (salonError || !salonData) {
      console.error("Error fetching salon data:", salonError);
      return new Response(
        JSON.stringify({ error: "Failed to fetch salon data" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        },
      );
    }

    const salon = salonData as Salon;

    // If WhatsApp is not enabled for this salon, skip processing
    if (!salon.whatsapp_enabled) {
      return new Response(
        JSON.stringify({
          message: "WhatsApp notifications not enabled for this salon",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        },
      );
    }

    // Get the client information
    const { data: clientData, error: clientError } = await supabaseClient
      .from("clients")
      .select("*")
      .eq("client_id", payload.record.client_id)
      .single();

    if (clientError || !clientData) {
      console.error("Error fetching client data:", clientError);
      return new Response(
        JSON.stringify({ error: "Failed to fetch client data" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        },
      );
    }

    const client = clientData as Client;

    // Get services for this appointment
    const { data: servicesData, error: servicesError } = await supabaseClient
      .from("appointment_services")
      .select("services(service_id, name, price, duration)")
      .eq("appointment_id", payload.record.appointment_id);

    if (servicesError) {
      console.error("Error fetching services data:", servicesError);
      return new Response(
        JSON.stringify({ error: "Failed to fetch services data" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        },
      );
    }

    const services = servicesData.map((item: any) =>
      item.services
    ) as Service[];

    // Get staff information if available
    let staffName = "Not assigned";
    if (payload.record.staff_id) {
      const { data: staffData, error: staffError } = await supabaseClient
        .from("users")
        .select("first_name, last_name")
        .eq("user_id", payload.record.staff_id)
        .single();

      if (!staffError && staffData) {
        staffName = `${staffData.first_name} ${staffData.last_name}`;
      }
    }

    // Format appointment date
    const appointmentDate = new Date(payload.record.appointment_date);
    const formattedDate = appointmentDate.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
    const formattedTime = appointmentDate.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });

    // Calculate total price
    const totalPrice = services.reduce(
      (sum, service) => sum + service.price,
      0,
    );

    // Determine the type of notification to send
    let message = "";
    let notificationType = "";

    if (payload.type === "INSERT" && payload.record.status === "scheduled") {
      // New appointment created
      notificationType = "appointment_created";
      message =
        `✨ *Appointment Confirmed!* ✨\n\n👋 Hi ${client.first_name}!\n\nYour appointment at *${salon.name}* has been confirmed! 🎉\n\n📅 *Date & Time:*\n${formattedDate} at ${formattedTime}\n\n💅 *Services:*\n${
          services.map((s) => `• ${s.name} - ₹${s.price}`).join("\n")
        }\n\n💰 *Total Amount: ₹${totalPrice}*\n\n👨‍💼 *Your Stylist: ${staffName}*\n\n${
          salon.address ? `📍 *Location:*\n${salon.address}\n\n` : ""
        }We can't wait to pamper you! 💖\n\n_✨ Powered by Salon Orbit Lite_`;
    } else if (
      payload.type === "UPDATE" && payload.record.status === "cancelled" &&
      payload.old_record?.status !== "cancelled"
    ) {
      // Appointment cancelled
      notificationType = "appointment_cancelled";
      message =
        `❌ *Appointment Cancelled* ❌\n\n👋 Hi ${client.first_name},\n\nWe're sorry to inform you that your appointment at *${salon.name}* has been cancelled. 😔\n\n📅 *Cancelled Appointment:*\n${formattedDate} at ${formattedTime}\n\n💬 If you'd like to reschedule, please contact us! We'd love to see you soon. 💕\n\n_✨ Powered by Salon Orbit Lite_`;
    } else if (
      payload.type === "UPDATE" && payload.record.status === "checked-in" &&
      payload.old_record?.status === "scheduled"
    ) {
      // Appointment check-in
      notificationType = "appointment_checkin";
      message =
        `🎯 *Welcome! You're Checked In* 🎯\n\n👋 Hi ${client.first_name}!\n\nYou've been successfully checked in at *${salon.name}*! 🏪\n\n💅 *Today's Services:*\n${
          services.map((s) => `• ${s.name} - ₹${s.price}`).join("\n")
        }\n\n💰 *Total Amount: ₹${totalPrice}*\n\nSit back, relax, and let us take care of you! ✨\n\n_✨ Powered by Salon Orbit Lite_`;
    } else if (
      payload.type === "UPDATE" && payload.record.status === "completed" &&
      payload.old_record?.status !== "completed"
    ) {
      // Appointment completed (checkout)
      notificationType = "appointment_completed";
      message =
        `🌟 *Thank You for Visiting!* 🌟\n\n👋 Hi ${client.first_name}!\n\nThank you for choosing *${salon.name}*! We hope you love your new look! 💇‍♀️✨\n\n💅 *Services Completed:*\n${
          services.map((s) => `• ${s.name} - ₹${s.price}`).join("\n")
        }\n\n💰 *Total Paid: ₹${totalPrice}*\n\n⭐ We hope you had an amazing experience! Can't wait to see you again soon! 💖\n\n📸 Don't forget to share your new look! #SalonOrbitLite\n\n_✨ Powered by Salon Orbit Lite_`;
    } else if (
      payload.type === "UPDATE" &&
      payload.record.appointment_date !== payload.old_record?.appointment_date
    ) {
      // Appointment rescheduled
      notificationType = "appointment_rescheduled";

      // Format old appointment date if available
      let oldDateInfo = "";
      if (payload.old_record?.appointment_date) {
        const oldDate = new Date(payload.old_record.appointment_date);
        const oldFormattedDate = oldDate.toLocaleDateString("en-US", {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        });
        const oldFormattedTime = oldDate.toLocaleTimeString("en-US", {
          hour: "2-digit",
          minute: "2-digit",
        });
        oldDateInfo = `${oldFormattedDate} at ${oldFormattedTime}`;
      }

      message =
        `🔄 *Appointment Rescheduled* 🔄\n\n👋 Hi ${client.first_name}!\n\nYour appointment at *${salon.name}* has been rescheduled! 📅\n\n${
          oldDateInfo ? `❌ *Previous Time:*\n${oldDateInfo}\n\n` : ""
        }✅ *New Time:*\n${formattedDate} at ${formattedTime}\n\n💅 *Services:*\n${
          services.map((s) => `• ${s.name} - ₹${s.price}`).join("\n")
        }\n\n💰 *Total Amount: ₹${totalPrice}*\n\n👨‍💼 *Your Stylist: ${staffName}*\n\n💬 Need to make changes? Just let us know! We're here to help. 😊\n\n_✨ Powered by Salon Orbit Lite_`;
    } else {
      // No notification needed for this event
      return new Response(
        JSON.stringify({ message: "No notification needed for this event" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        },
      );
    }

    // Queue WhatsApp message instead of sending directly
    if (message) {
      try {
        // Format phone number (remove any non-digit characters and ensure it starts with country code)
        let phoneNumber = client.phone.replace(/\D/g, "");
        if (!phoneNumber.startsWith("91") && phoneNumber.length === 10) {
          phoneNumber = "91" + phoneNumber;
        }

        console.log(
          `📱 Sending WhatsApp message directly to ${phoneNumber} (salon: ${salon.salon_id})`,
        );
        console.log(`📝 Message type: ${notificationType}`);

        // Try direct sending first, fallback to queue if it fails
        const USE_DIRECT_SENDING_WITH_FALLBACK = true;

        if (USE_DIRECT_SENDING_WITH_FALLBACK) {
          // Try sending WhatsApp message directly first
          try {
            console.log(
              `🚀 Attempting direct WhatsApp message to ${phoneNumber} (salon: ${salon.salon_id})`,
            );

            // Add a small delay to prevent rapid-fire requests that cause conflicts
            await new Promise((resolve) => setTimeout(resolve, 1000));

            const whatsappResponse = await fetch(
              `https://wb-userbot-production.up.railway.app/session/${salon.salon_id}/send`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  phoneNumber: phoneNumber,
                  message: message,
                }),
              },
            );

            const whatsappResult = await whatsappResponse.json();

            if (whatsappResponse.ok) {
              console.log(
                `✅ WhatsApp message sent directly to ${phoneNumber}`,
              );

              return new Response(
                JSON.stringify({
                  success: true,
                  message: `${notificationType} notification sent successfully`,
                  recipient: phoneNumber,
                  notification_type: notificationType,
                  delivery_method: "direct",
                  whatsapp_result: whatsappResult,
                }),
                {
                  headers: {
                    ...corsHeaders,
                    "Content-Type": "application/json",
                  },
                  status: 200,
                },
              );
            } else {
              // Direct sending failed, fallback to queue
              console.log(
                `⚠️ Direct sending failed, falling back to queue. Error: ${
                  whatsappResult.message || "Unknown error"
                }`,
              );

              // Fall through to queue the message
            }
          } catch (error) {
            // Direct sending failed, fallback to queue
            console.log(
              `⚠️ Direct sending error, falling back to queue. Error: ${error.message}`,
            );

            // Fall through to queue the message
          }
        }

        // Queue WhatsApp message (either as primary method or fallback)
        try {
          console.log(
            `📥 Queueing WhatsApp message for ${phoneNumber} (salon: ${salon.salon_id})`,
          );

          const { error: queueError } = await supabaseClient
            .from("whatsapp_message_queue")
            .insert({
              salon_id: salon.salon_id,
              phone_number: phoneNumber,
              message: message,
              message_type: notificationType,
              appointment_id: payload.record.appointment_id,
              status: "pending",
              retry_count: 0,
              max_retries: 3,
              next_retry_at: new Date().toISOString(),
            });

          if (queueError) {
            console.error("❌ Failed to queue WhatsApp message:", queueError);
            throw new Error(`Failed to queue message: ${queueError.message}`);
          }

          console.log(
            `✅ WhatsApp message queued successfully for ${phoneNumber}`,
          );

          return new Response(
            JSON.stringify({
              success: true,
              message: `${notificationType} notification queued successfully`,
              recipient: phoneNumber,
              notification_type: notificationType,
              delivery_method: "queued",
            }),
            {
              headers: { ...corsHeaders, "Content-Type": "application/json" },
              status: 200,
            },
          );
        } catch (error) {
          console.error("❌ Error queueing WhatsApp message:", error);

          return new Response(
            JSON.stringify({
              success: false,
              message: `Failed to queue ${notificationType} notification`,
              error: error.message,
              recipient: phoneNumber,
              notification_type: notificationType,
              delivery_method: "queued",
            }),
            {
              headers: { ...corsHeaders, "Content-Type": "application/json" },
              status: 500,
            },
          );
        }
      } catch (error) {
        console.error("Error sending WhatsApp message:", error);
        return new Response(
          JSON.stringify({
            error: "Failed to send WhatsApp message",
            details: error.message,
          }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 500,
          },
        );
      }
    }

    return new Response(
      JSON.stringify({ message: "Event processed but no notification sent" }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      },
    );
  } catch (error) {
    console.error("Error processing request:", error);
    return new Response(
      JSON.stringify({
        error: "Internal Server Error",
        details: error.message,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      },
    );
  }
});
