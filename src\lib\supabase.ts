import { createClient } from '@supabase/supabase-js';
import type { Database } from '../types/database';

const supabaseUrl = 'https://mixjfinrxzpplzqidlas.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1peGpmaW5yeHpwcGx6cWlkbGFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODY0MzgsImV4cCI6MjA1OTE2MjQzOH0.g4lTxBEZbG_GJKy_WrBJwW0H2tr9XaZHLzXpjdtbCzA';

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: localStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Helper function to get the current user's salon ID
export const getCurrentUserSalonId = async (): Promise<string | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return null;

    try {
      // Try to get salon ID from the users table
      const { data, error } = await supabase
        .from('users')
        .select('salon_id')
        .eq('user_id', user.id)
        .single();

      if (!error && data && data.salon_id) {
        return data.salon_id;
      }
    } catch (dbError) {
      console.error('Error querying users table:', dbError);
    }

    // If we couldn't get the salon ID from the database, try to get it from the user metadata
    const userData = user.user_metadata || {};
    return userData.salon_id || null;
  } catch (error) {
    console.error('Error getting current user salon ID:', error);
    return null;
  }
};
