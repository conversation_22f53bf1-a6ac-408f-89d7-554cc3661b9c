import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../lib/supabase.config';
import type { User } from '../types/database';

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  authUser: any;
  loading: boolean;
  login: (email: string, password: string) => Promise<{ error?: any }>;
  signup: (email: string, password: string, userData: any) => Promise<{ error?: any, user?: any }>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error?: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [authUser, setAuthUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Add a state variable to track if the initial auth check has completed
  const [initialAuthCheckComplete, setInitialAuthCheckComplete] = useState(false);

  // Log when initialAuthCheckComplete changes
  useEffect(() => {
    console.log('initialAuthCheckComplete changed to:', initialAuthCheckComplete);
  }, [initialAuthCheckComplete]);

  useEffect(() => {
    const checkUser = async () => {
      try {
        console.log('Checking for existing session...');

        // First check if we have a session
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Error getting session:', sessionError);
          setAuthUser(null);
          setUser(null);
          setIsAuthenticated(false);
          setLoading(false);
          setInitialAuthCheckComplete(true);
          console.log('Initial auth check complete (error): isAuthenticated =', false);
          return;
        }

        if (!sessionData.session) {
          console.log('No active session found');
          setAuthUser(null);
          setUser(null);
          setIsAuthenticated(false);
          setLoading(false);
          setInitialAuthCheckComplete(true);
          console.log('Initial auth check complete (no session): isAuthenticated =', false);
          return;
        }

        console.log('Session found, getting user data...', sessionData.session.user.id);
        try {
          const userData = await getCurrentUser();

          if (userData) {
            console.log('User data retrieved successfully');
            setAuthUser(userData.authUser);
            setUser(userData.profileData);
            setIsAuthenticated(true);
            console.log('Initial auth check complete (with user data): isAuthenticated =', true);
          } else {
            console.log('No user data found despite having a session');
            // Even without user data, we should still set isAuthenticated to true
            // since we have a valid session
            setAuthUser(sessionData.session.user);
            setIsAuthenticated(true);
            console.log('Initial auth check complete (session only): isAuthenticated =', true);
          }
        } catch (userDataError) {
          console.error('Error getting user data:', userDataError);
          // Even with an error, we should still set isAuthenticated to true
          // since we have a valid session
          setAuthUser(sessionData.session.user);
          setIsAuthenticated(true);
          console.log('Initial auth check complete (with error): isAuthenticated =', true);
        }
      } catch (error) {
        console.error('Error checking user:', error);
        setAuthUser(null);
        setUser(null);
        setIsAuthenticated(false);
        console.log('Initial auth check complete (global error): isAuthenticated =', false);
      } finally {
        setLoading(false);
        setInitialAuthCheckComplete(true);
        console.log('Initial auth check complete, loading set to false');
      }
    };

    // Check user on initial load
    checkUser();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, !!session);

        console.log('Auth state change event:', event, 'initialAuthCheckComplete:', initialAuthCheckComplete);

        // Only process auth state changes after the initial check is complete
        // or if this is a SIGNED_OUT or SIGNED_IN event (which should always be processed)
        if (initialAuthCheckComplete || event === 'SIGNED_OUT' || event === 'SIGNED_IN') {
          console.log('Processing auth state change event:', event);
          // Set loading to true while we process the auth state change
          setLoading(true);

          try {
            if (session) {
              console.log('Session found in auth state change, getting user data...', session.user.id);
              try {
                const userData = await getCurrentUser();
                if (userData) {
                  console.log('User data retrieved successfully in auth state change');
                  setAuthUser(userData.authUser);
                  setUser(userData.profileData);
                  setIsAuthenticated(true);
                  console.log('Auth state updated: isAuthenticated =', true);
                } else {
                  console.log('No user data found in auth state change despite having a session');
                  // Even without user data, we should still set isAuthenticated to true
                  // since we have a valid session
                  setAuthUser(session.user);
                  setIsAuthenticated(true);
                  console.log('Auth state updated with minimal data: isAuthenticated =', true);
                }
              } catch (error) {
                console.error('Error getting user data after auth state change:', error);
                // Even with an error, we should still set isAuthenticated to true
                // since we have a valid session
                setAuthUser(session.user);
                setIsAuthenticated(true);
                console.log('Auth state updated despite error: isAuthenticated =', true);
              }
            } else {
              console.log('No session found in auth state change');
              setAuthUser(null);
              setUser(null);
              setIsAuthenticated(false);
              console.log('Auth state updated: isAuthenticated =', false);
            }
          } finally {
            // Always set loading to false when we're done processing
            console.log('Auth state change processing complete, setting loading to false');
            setLoading(false);
          }
        } else {
          console.log('Skipping auth state change processing until initial check completes');
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const login = async (email: string, password: string) => {
    try {
      console.log('Attempting to login with email:', email);

      // Set loading state to true during login
      setLoading(true);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Login error from Supabase:', error);
        setLoading(false); // Reset loading state on error
        return { error };
      }

      console.log('Login successful, session created');

      // IMPORTANT: Directly set authentication state
      setAuthUser(data.user);
      setIsAuthenticated(true);
      setInitialAuthCheckComplete(true);
      console.log('Authentication state directly set: isAuthenticated =', true);

      // Set loading to false
      setLoading(false);

      // Return success with the user
      return { error: null, success: true, user: data.user };
    } catch (error) {
      console.error('Login error:', error);
      setLoading(false); // Reset loading state on error
      return { error };
    }
  };

  const signup = async (email: string, password: string, userData: any) => {
    try {
      console.log('Attempting to signup with email:', email);

      // Set loading state to true during signup
      setLoading(true);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      });

      if (error) {
        console.error('Signup error from Supabase:', error);
        setLoading(false); // Reset loading state on error
        return { error };
      }

      console.log('Signup successful, user created:', data.user?.id);

      // For signup, we set loading to false here because we might not get an auth state change event
      // if email confirmation is required
      setLoading(false);

      return { error: null, user: data.user };
    } catch (error) {
      console.error('Signup error:', error);
      setLoading(false); // Reset loading state on error
      return { error };
    }
  };

  const logout = async () => {
    try {
      console.log('Attempting to logout');

      // Set loading state to true during logout
      setLoading(true);

      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Logout error from Supabase:', error);
      } else {
        console.log('Logout successful');
      }

      // Always reset the state, even if there was an error
      setIsAuthenticated(false);
      setUser(null);
      setAuthUser(null);
      setLoading(false); // Reset loading state after logout
    } catch (error) {
      console.error('Logout error:', error);

      // Always reset the state, even if there was an error
      setIsAuthenticated(false);
      setUser(null);
      setAuthUser(null);
      setLoading(false); // Reset loading state after logout
    }
  };

  const value = {
    isAuthenticated,
    user,
    authUser,
    loading,
    login,
    signup,
    logout,
    initialAuthCheckComplete,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
